#!/usr/bin/env python3
"""
Log Similarity Analysis using Doc2Vec

This module provides functionality to:
1. Chunk log files into segments of approximately 10,000 tokens
2. Train a Doc2Vec model where chunks from the same log file share the same tag
3. Find similar log files based on document embeddings
"""

import os
import re
import nltk
from nltk.tokenize import word_tokenize
from gensim.models.doc2vec import Doc2Vec, TaggedDocument
from typing import List, Dict, Tuple, Generator
import logging
from pathlib import Path
import pickle

# Download required NLTK data
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class LogChunker:
    """Handles chunking of log files into token-based segments."""
    
    def __init__(self, chunk_size: int = 10000):
        """
        Initialize the LogChunker.
        
        Args:
            chunk_size: Maximum number of tokens per chunk
        """
        self.chunk_size = chunk_size
    
    def preprocess_log_line(self, line: str) -> str:
        """
        Preprocess a single log line by removing timestamps and normalizing.
        
        Args:
            line: Raw log line
            
        Returns:
            Preprocessed log line
        """
        # Remove common timestamp patterns
        line = re.sub(r'\d{4}-\d{2}-\d{2}[\s\T]\d{2}:\d{2}:\d{2}[.,]\d{3}', '', line)
        line = re.sub(r'\d{2}/\d{2}/\d{4}\s\d{2}:\d{2}:\d{2}', '', line)
        line = re.sub(r'\[\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2}\]', '', line)
        
        # Remove IP addresses
        line = re.sub(r'\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b', 'IP_ADDRESS', line)
        
        # Remove URLs
        line = re.sub(r'https?://[^\s]+', 'URL', line)
        
        # Normalize whitespace
        line = re.sub(r'\s+', ' ', line.strip())
        
        return line
    
    def tokenize_text(self, text: str) -> List[str]:
        """
        Tokenize text using NLTK word tokenizer.
        
        Args:
            text: Input text to tokenize
            
        Returns:
            List of tokens
        """
        try:
            tokens = word_tokenize(text.lower())
            # Filter out non-alphabetic tokens and very short tokens
            tokens = [token for token in tokens if token.isalpha() and len(token) > 2]
            return tokens
        except Exception as e:
            logger.warning(f"Error tokenizing text: {e}")
            return text.lower().split()
    
    def chunk_log_file(self, file_path: str) -> Generator[Tuple[List[str], str], None, None]:
        """
        Chunk a log file into segments of approximately chunk_size tokens.
        
        Args:
            file_path: Path to the log file
            
        Yields:
            Tuple of (tokens, tag) where tag identifies the source file
        """
        file_name = Path(file_path).stem
        chunk_tokens = []
        chunk_count = 0
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
                for line_num, line in enumerate(file, 1):
                    if not line.strip():
                        continue
                    
                    # Preprocess the line
                    processed_line = self.preprocess_log_line(line)
                    if not processed_line:
                        continue
                    
                    # Tokenize the line
                    line_tokens = self.tokenize_text(processed_line)
                    
                    # Add tokens to current chunk
                    chunk_tokens.extend(line_tokens)
                    
                    # If chunk is large enough, yield it
                    if len(chunk_tokens) >= self.chunk_size:
                        tag = f"{file_name}_chunk_{chunk_count}"
                        yield chunk_tokens[:self.chunk_size], tag
                        
                        # Keep remaining tokens for next chunk
                        chunk_tokens = chunk_tokens[self.chunk_size:]
                        chunk_count += 1
                
                # Yield remaining tokens if any
                if chunk_tokens:
                    tag = f"{file_name}_chunk_{chunk_count}"
                    yield chunk_tokens, tag
                    
        except Exception as e:
            logger.error(f"Error processing file {file_path}: {e}")


class Doc2VecLogSimilarity:
    """Main class for training Doc2Vec model and finding similar logs."""
    
    def __init__(self, chunk_size: int = 10000, vector_size: int = 100, 
                 window: int = 5, min_count: int = 2, workers: int = 4, epochs: int = 20):
        """
        Initialize the Doc2Vec log similarity analyzer.
        
        Args:
            chunk_size: Number of tokens per chunk
            vector_size: Dimensionality of the feature vectors
            window: Maximum distance between current and predicted word
            min_count: Ignores all words with total frequency lower than this
            workers: Number of worker threads
            epochs: Number of training epochs
        """
        self.chunker = LogChunker(chunk_size)
        self.model = None
        self.tagged_documents = []
        
        # Doc2Vec parameters
        self.vector_size = vector_size
        self.window = window
        self.min_count = min_count
        self.workers = workers
        self.epochs = epochs
        
        # Keep track of file mappings
        self.file_to_tags = {}  # Maps file names to their chunk tags
        self.tag_to_file = {}   # Maps chunk tags to file names
    
    def prepare_training_data(self, log_files: List[str]) -> None:
        """
        Prepare training data from log files.
        
        Args:
            log_files: List of paths to log files
        """
        logger.info(f"Preparing training data from {len(log_files)} log files...")
        
        self.tagged_documents = []
        self.file_to_tags = {}
        self.tag_to_file = {}
        
        for file_path in log_files:
            file_name = Path(file_path).stem
            file_tags = []
            
            logger.info(f"Processing file: {file_path}")
            
            for tokens, tag in self.chunker.chunk_log_file(file_path):
                if len(tokens) < 10:  # Skip very small chunks
                    continue
                
                # Create TaggedDocument with file-specific tag
                tagged_doc = TaggedDocument(words=tokens, tags=[tag, file_name])
                self.tagged_documents.append(tagged_doc)
                
                # Update mappings
                file_tags.append(tag)
                self.tag_to_file[tag] = file_name
            
            self.file_to_tags[file_name] = file_tags
            logger.info(f"Created {len(file_tags)} chunks for {file_name}")
        
        logger.info(f"Total tagged documents created: {len(self.tagged_documents)}")
    
    def train_model(self) -> None:
        """Train the Doc2Vec model on the prepared data."""
        if not self.tagged_documents:
            raise ValueError("No training data available. Call prepare_training_data() first.")
        
        logger.info("Training Doc2Vec model...")
        
        # Initialize the model
        self.model = Doc2Vec(
            vector_size=self.vector_size,
            window=self.window,
            min_count=self.min_count,
            workers=self.workers,
            epochs=self.epochs,
            dm=1,  # Use distributed memory (PV-DM)
            dbow_words=1  # Train word vectors as well
        )
        
        # Build vocabulary
        logger.info("Building vocabulary...")
        self.model.build_vocab(self.tagged_documents)
        
        # Train the model
        logger.info(f"Training model for {self.epochs} epochs...")
        self.model.train(self.tagged_documents, 
                        total_examples=self.model.corpus_count, 
                        epochs=self.model.epochs)
        
        logger.info("Model training completed!")
    
    def save_model(self, model_path: str) -> None:
        """Save the trained model and mappings."""
        if self.model is None:
            raise ValueError("No trained model to save.")
        
        # Save the Doc2Vec model
        self.model.save(model_path)
        
        # Save the mappings
        mappings = {
            'file_to_tags': self.file_to_tags,
            'tag_to_file': self.tag_to_file
        }
        
        with open(f"{model_path}.mappings", 'wb') as f:
            pickle.dump(mappings, f)
        
        logger.info(f"Model and mappings saved to {model_path}")
    
    def load_model(self, model_path: str) -> None:
        """Load a trained model and mappings."""
        self.model = Doc2Vec.load(model_path)
        
        with open(f"{model_path}.mappings", 'rb') as f:
            mappings = pickle.load(f)
            self.file_to_tags = mappings['file_to_tags']
            self.tag_to_file = mappings['tag_to_file']
        
        logger.info(f"Model and mappings loaded from {model_path}")


def main():
    """Example usage of the Log Similarity system."""
    # Initialize the similarity analyzer
    analyzer = Doc2VecLogSimilarity(chunk_size=10000, epochs=20)
    
    # Example: Prepare training data (replace with your log file paths)
    log_files = [
        # Add your log file paths here
        # "logs/application.log",
        # "logs/error.log",
        # "logs/access.log"
    ]
    
    if not log_files:
        logger.warning("No log files specified. Please add log file paths to the log_files list.")
        return
    
    # Prepare and train
    analyzer.prepare_training_data(log_files)
    analyzer.train_model()
    
    # Save the model
    analyzer.save_model("log_similarity_model")
    
    logger.info("Training completed successfully!")


if __name__ == "__main__":
    main()
