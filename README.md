# Log Similarity Analysis with Doc2Vec

This project implements a log similarity analysis system using Doc2Vec (Document to Vector) embeddings. The system chunks log files into segments of approximately 10,000 tokens and trains a Doc2Vec model where chunks from the same log file share the same tag identifier.

## Features

- **Intelligent Log Chunking**: Splits log files into ~10,000 token chunks using NLTK tokenization
- **Preprocessing**: Removes timestamps, IP addresses, and URLs while normalizing text
- **Doc2Vec Training**: Trains embeddings where chunks from the same file share tags
- **Similarity Analysis**: Find similar log files and individual chunks
- **New File Analysis**: Analyze new log files against trained model

## Installation

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

2. The system will automatically download required NLTK data on first run.

## Quick Start

### 1. Prepare Your Log Files

Create a folder containing your log files:
```bash
mkdir logs
# Copy your log files to the logs folder
cp /path/to/your/*.log logs/
```

### 2. Train the Model

```bash
# Train on log files in the 'logs' folder
python example_usage.py --log-folder logs

# Or specify custom parameters
python example_usage.py --log-folder /path/to/logs --model-name my_model --epochs 30
```

This will:
- Find all log files in the specified folder
- Train a Doc2Vec model on the log files
- Save the trained model
- Show similarity analysis results

### 3. Find Similar Log Files

```bash
# Find files similar to a specific file in your trained model
python find_similar_logs.py --target-file application --top-k 5

# Analyze a new log file against the trained model
python find_similar_logs.py --new-file /path/to/new/log.log

# List all files in the trained model
python find_similar_logs.py --list-files
```

### 4. Programmatic Usage

```python
from log_similarity_doc2vec import Doc2VecLogSimilarity
from find_similar_logs import LogSimilarityFinder

# Train a model programmatically
analyzer = Doc2VecLogSimilarity(chunk_size=10000, epochs=20)
log_files = ["logs/app1.log", "logs/app2.log", "logs/error.log"]
analyzer.prepare_training_data(log_files)
analyzer.train_model()
analyzer.save_model("my_model")

# Use the trained model
finder = LogSimilarityFinder("my_model")
similar_files = finder.find_similar_files("app1", top_k=3)
results = finder.analyze_new_log_file("new_log.log")
```

## Command Line Options

### Training (example_usage.py)
```bash
python example_usage.py --help

Options:
  --log-folder, -f     Path to folder containing log files (default: logs)
  --model-name, -m     Name for the saved model (default: log_similarity_model)
  --chunk-size, -c     Number of tokens per chunk (default: 10000)
  --epochs, -e         Number of training epochs (default: 20)
  --vector-size, -v    Vector size for embeddings (default: 100)
  --extensions         File extensions to consider as log files
```

### Analysis (find_similar_logs.py)
```bash
python find_similar_logs.py --help

Options:
  --model, -m          Path to the trained model (default: log_similarity_model)
  --new-file, -n       Path to new log file to analyze
  --target-file, -t    Find files similar to this target file
  --top-k, -k          Number of similar files to return (default: 5)
  --list-files, -l     List all files in the trained model
```

## File Structure

- `log_similarity_doc2vec.py` - Main classes for chunking and training
- `find_similar_logs.py` - Similarity analysis and new file processing
- `example_usage.py` - Training script with folder-based input
- `requirements.txt` - Python dependencies

## Key Components

### LogChunker Class
- Preprocesses log lines (removes timestamps, IPs, URLs)
- Tokenizes text using NLTK
- Creates chunks of approximately 10,000 tokens
- Maintains file-to-chunk relationships

### Doc2VecLogSimilarity Class
- Manages Doc2Vec model training
- Creates TaggedDocuments with file-specific tags
- Handles model saving/loading
- Tracks file and chunk mappings

### LogSimilarityFinder Class
- Loads trained models
- Finds similar files using cosine similarity
- Analyzes new log files
- Provides model information

## How It Works

1. **Chunking**: Log files are split into ~10,000 token segments
2. **Tagging**: Each chunk gets a unique tag, but chunks from the same file share a common file identifier
3. **Training**: Doc2Vec learns embeddings for each chunk and file
4. **Similarity**: Cosine similarity between file vectors identifies similar logs

## Parameters

### Doc2Vec Parameters
- `chunk_size`: Number of tokens per chunk (default: 10000)
- `vector_size`: Embedding dimensions (default: 100)
- `window`: Context window size (default: 5)
- `min_count`: Minimum word frequency (default: 2)
- `epochs`: Training iterations (default: 20)

### Preprocessing Features
- Timestamp removal (multiple formats)
- IP address normalization
- URL replacement
- Whitespace normalization
- Token filtering (alphabetic, length > 2)

## Example Output

```
Files similar to 'web_server':
  web_server_2: 0.8945
  application: 0.7234
  database: 0.6123

New file analysis results:
  File: new_server.log
  Chunks created: 15
  Average similarity: 0.8234
  Most similar files:
    web_server: 0.8945
    web_server_2: 0.8234
    application: 0.7456
```

## Use Cases

- **Log Anomaly Detection**: Find unusual log patterns
- **Log Classification**: Group similar log types
- **Incident Analysis**: Find logs similar to known issues
- **System Monitoring**: Identify recurring patterns
- **Troubleshooting**: Locate similar error conditions

## Performance Notes

- Training time scales with number of files and chunks
- Memory usage depends on vocabulary size and vector dimensions
- Consider reducing `epochs` for faster training during development
- Use more `workers` for faster training on multi-core systems
