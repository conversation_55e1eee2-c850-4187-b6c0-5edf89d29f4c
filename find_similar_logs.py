#!/usr/bin/env python3
"""
Find Similar Log Files using trained Doc2Vec model

This script demonstrates how to use the trained Doc2Vec model to find
similar log files and chunks.
"""

import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
import logging
from log_similarity_doc2vec import Doc2VecLogSimilarity, LogChunker

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class LogSimilarityFinder:
    """Find similar logs using trained Doc2Vec model."""
    
    def __init__(self, model_path: str):
        """
        Initialize with a trained model.
        
        Args:
            model_path: Path to the saved Doc2Vec model
        """
        self.analyzer = Doc2VecLogSimilarity()
        self.analyzer.load_model(model_path)
        self.chunker = LogChunker()
    
    def get_file_vector(self, file_name: str) -> np.ndarray:
        """
        Get the average vector representation of a log file.
        
        Args:
            file_name: Name of the log file (without extension)
            
        Returns:
            Average vector representation of the file
        """
        if file_name not in self.analyzer.file_to_tags:
            raise ValueError(f"File {file_name} not found in trained model")
        
        chunk_tags = self.analyzer.file_to_tags[file_name]
        vectors = []
        
        for tag in chunk_tags:
            try:
                vector = self.analyzer.model.dv[tag]
                vectors.append(vector)
            except KeyError:
                logger.warning(f"Vector for tag {tag} not found")
        
        if not vectors:
            raise ValueError(f"No vectors found for file {file_name}")
        
        return np.mean(vectors, axis=0)
    
    def find_similar_files(self, target_file: str, top_k: int = 5) -> list:
        """
        Find files most similar to the target file.
        
        Args:
            target_file: Name of the target file (without extension)
            top_k: Number of similar files to return
            
        Returns:
            List of tuples (file_name, similarity_score)
        """
        target_vector = self.get_file_vector(target_file)
        similarities = []
        
        for file_name in self.analyzer.file_to_tags.keys():
            if file_name == target_file:
                continue
            
            try:
                file_vector = self.get_file_vector(file_name)
                similarity = cosine_similarity([target_vector], [file_vector])[0][0]
                similarities.append((file_name, similarity))
            except Exception as e:
                logger.warning(f"Error calculating similarity for {file_name}: {e}")
        
        # Sort by similarity (descending)
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        return similarities[:top_k]
    
    def find_similar_chunks(self, target_chunk_tag: str, top_k: int = 10) -> list:
        """
        Find chunks most similar to the target chunk.
        
        Args:
            target_chunk_tag: Tag of the target chunk
            top_k: Number of similar chunks to return
            
        Returns:
            List of tuples (chunk_tag, similarity_score, source_file)
        """
        try:
            target_vector = self.analyzer.model.dv[target_chunk_tag]
        except KeyError:
            raise ValueError(f"Chunk tag {target_chunk_tag} not found in model")
        
        similarities = []
        
        for tag in self.analyzer.tag_to_file.keys():
            if tag == target_chunk_tag:
                continue
            
            try:
                chunk_vector = self.analyzer.model.dv[tag]
                similarity = cosine_similarity([target_vector], [chunk_vector])[0][0]
                source_file = self.analyzer.tag_to_file[tag]
                similarities.append((tag, similarity, source_file))
            except KeyError:
                continue
        
        # Sort by similarity (descending)
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        return similarities[:top_k]
    
    def analyze_new_log_file(self, file_path: str, top_k: int = 5) -> dict:
        """
        Analyze a new log file and find similar existing files.
        
        Args:
            file_path: Path to the new log file
            top_k: Number of similar files to return
            
        Returns:
            Dictionary with analysis results
        """
        logger.info(f"Analyzing new log file: {file_path}")
        
        # Chunk the new file
        chunks = []
        for tokens, _ in self.chunker.chunk_log_file(file_path):
            if len(tokens) >= 10:  # Skip very small chunks
                chunks.append(tokens)
        
        if not chunks:
            return {"error": "No valid chunks found in the file"}
        
        # Get vectors for each chunk
        chunk_vectors = []
        for chunk in chunks:
            try:
                # Infer vector for the new chunk
                vector = self.analyzer.model.infer_vector(chunk)
                chunk_vectors.append(vector)
            except Exception as e:
                logger.warning(f"Error inferring vector for chunk: {e}")
        
        if not chunk_vectors:
            return {"error": "Could not infer vectors for any chunks"}
        
        # Calculate average vector for the file
        file_vector = np.mean(chunk_vectors, axis=0)
        
        # Find similar files
        similarities = []
        for file_name in self.analyzer.file_to_tags.keys():
            try:
                existing_file_vector = self.get_file_vector(file_name)
                similarity = cosine_similarity([file_vector], [existing_file_vector])[0][0]
                similarities.append((file_name, similarity))
            except Exception as e:
                logger.warning(f"Error calculating similarity with {file_name}: {e}")
        
        # Sort by similarity
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        return {
            "file_path": file_path,
            "num_chunks": len(chunks),
            "similar_files": similarities[:top_k],
            "avg_similarity": np.mean([sim[1] for sim in similarities[:top_k]]) if similarities else 0
        }
    
    def get_model_info(self) -> dict:
        """Get information about the loaded model."""
        return {
            "total_files": len(self.analyzer.file_to_tags),
            "total_chunks": len(self.analyzer.tag_to_file),
            "vector_size": self.analyzer.model.vector_size,
            "vocabulary_size": len(self.analyzer.model.wv.key_to_index),
            "files": list(self.analyzer.file_to_tags.keys())
        }


def main():
    """Command line interface for the similarity finder."""
    import argparse

    parser = argparse.ArgumentParser(description='Find similar log files using trained Doc2Vec model')
    parser.add_argument('--model', '-m', type=str, default='log_similarity_model',
                       help='Path to the trained model (default: log_similarity_model)')
    parser.add_argument('--new-file', '-n', type=str,
                       help='Path to new log file to analyze')
    parser.add_argument('--target-file', '-t', type=str,
                       help='Find files similar to this target file (use filename without extension)')
    parser.add_argument('--top-k', '-k', type=int, default=5,
                       help='Number of similar files to return (default: 5)')
    parser.add_argument('--list-files', '-l', action='store_true',
                       help='List all files in the trained model')

    args = parser.parse_args()

    try:
        # Initialize the similarity finder
        finder = LogSimilarityFinder(args.model)

        # Get model information
        info = finder.get_model_info()
        logger.info(f"Loaded model with {info['total_files']} files and {info['total_chunks']} chunks")
        logger.info(f"Vocabulary size: {info['vocabulary_size']} words")

        if args.list_files:
            logger.info("Available files in the model:")
            for i, file_name in enumerate(info['files'], 1):
                logger.info(f"  {i}. {file_name}")
            return

        if args.new_file:
            logger.info(f"Analyzing new log file: {args.new_file}")
            results = finder.analyze_new_log_file(args.new_file, top_k=args.top_k)

            if "error" not in results:
                logger.info(f"Analysis results:")
                logger.info(f"  File: {results['file_path']}")
                logger.info(f"  Chunks created: {results['num_chunks']}")
                logger.info(f"  Average similarity: {results['avg_similarity']:.4f}")
                logger.info(f"  Most similar files:")

                for file_name, similarity in results['similar_files']:
                    logger.info(f"    {file_name}: {similarity:.4f}")
            else:
                logger.error(f"Error analyzing file: {results['error']}")

        elif args.target_file:
            if args.target_file not in info['files']:
                logger.error(f"Target file '{args.target_file}' not found in model")
                logger.info(f"Available files: {info['files']}")
                return

            logger.info(f"Finding files similar to: {args.target_file}")
            similar_files = finder.find_similar_files(args.target_file, top_k=args.top_k)

            if similar_files:
                logger.info("Most similar files:")
                for file_name, similarity in similar_files:
                    logger.info(f"  {file_name}: {similarity:.4f}")
            else:
                logger.info("No similar files found")

        else:
            # Default behavior: show model info and example
            logger.info(f"Available files: {info['files']}")

            if info['files']:
                target_file = info['files'][0]  # Use first file as example
                logger.info(f"\nExample: Finding files similar to '{target_file}':")

                similar_files = finder.find_similar_files(target_file, top_k=min(3, len(info['files'])-1))

                if similar_files:
                    for file_name, similarity in similar_files:
                        logger.info(f"  {file_name}: {similarity:.4f}")
                else:
                    logger.info("  No similar files found")

            logger.info(f"\nUsage examples:")
            logger.info(f"  python {__file__} --new-file /path/to/new/log.log")
            logger.info(f"  python {__file__} --target-file application --top-k 3")
            logger.info(f"  python {__file__} --list-files")

    except FileNotFoundError:
        logger.error(f"Model file {args.model} not found. Please train the model first.")
        logger.info(f"Run: python example_usage.py --log-folder /path/to/logs")
    except Exception as e:
        logger.error(f"Error: {e}")


if __name__ == "__main__":
    main()
