#!/usr/bin/env python3
"""
Example usage of the Log Similarity system

This script demonstrates the complete workflow:
1. Read log files from a specified folder
2. Train the Doc2Vec model
3. Find similar log files
"""

import os
import argparse
from pathlib import Path
from log_similarity_doc2vec import Doc2VecLogSimilarity
from find_similar_logs import LogSimilarityFinder
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def find_log_files(log_folder: str, extensions: list = None) -> list:
    """
    Find all log files in the specified folder.

    Args:
        log_folder: Path to the folder containing log files
        extensions: List of file extensions to consider (default: common log extensions)

    Returns:
        List of paths to log files
    """
    if extensions is None:
        extensions = ['.log', '.txt', '.out', '.err', '.trace']

    log_files = []
    log_folder_path = Path(log_folder)

    if not log_folder_path.exists():
        logger.error(f"Log folder does not exist: {log_folder}")
        return []

    if not log_folder_path.is_dir():
        logger.error(f"Path is not a directory: {log_folder}")
        return []

    # Search for log files
    for file_path in log_folder_path.iterdir():
        if file_path.is_file():
            # Check if file has a log-like extension
            if any(file_path.suffix.lower() == ext for ext in extensions):
                log_files.append(str(file_path))
            # Also include files with 'log' in the name (even without extension)
            elif 'log' in file_path.name.lower():
                log_files.append(str(file_path))

    # Also search in subdirectories (one level deep)
    for subdir in log_folder_path.iterdir():
        if subdir.is_dir():
            for file_path in subdir.iterdir():
                if file_path.is_file():
                    if any(file_path.suffix.lower() == ext for ext in extensions):
                        log_files.append(str(file_path))
                    elif 'log' in file_path.name.lower():
                        log_files.append(str(file_path))

    log_files.sort()  # Sort for consistent ordering
    return log_files


def validate_log_files(log_files: list) -> list:
    """
    Validate log files and filter out empty or unreadable files.

    Args:
        log_files: List of log file paths

    Returns:
        List of valid log file paths
    """
    valid_files = []

    for file_path in log_files:
        try:
            file_size = os.path.getsize(file_path)
            if file_size == 0:
                logger.warning(f"Skipping empty file: {file_path}")
                continue

            # Try to read a few lines to ensure file is readable
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines_read = 0
                for line in f:
                    lines_read += 1
                    if lines_read >= 5:  # Read first 5 lines to test
                        break

                if lines_read == 0:
                    logger.warning(f"Skipping file with no readable content: {file_path}")
                    continue

            valid_files.append(file_path)
            logger.info(f"Valid log file found: {file_path} ({file_size} bytes)")

        except Exception as e:
            logger.warning(f"Skipping unreadable file {file_path}: {e}")

    return valid_files


def main():
    """Run the complete example workflow."""

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Train Doc2Vec model on log files')
    parser.add_argument('--log-folder', '-f', type=str, default='logs',
                       help='Path to folder containing log files (default: logs)')
    parser.add_argument('--model-name', '-m', type=str, default='log_similarity_model',
                       help='Name for the saved model (default: log_similarity_model)')
    parser.add_argument('--chunk-size', '-c', type=int, default=10000,
                       help='Number of tokens per chunk (default: 10000)')
    parser.add_argument('--epochs', '-e', type=int, default=20,
                       help='Number of training epochs (default: 20)')
    parser.add_argument('--vector-size', '-v', type=int, default=100,
                       help='Vector size for embeddings (default: 100)')
    parser.add_argument('--extensions', nargs='+', default=['.log', '.txt', '.out', '.err', '.trace'],
                       help='File extensions to consider as log files')

    args = parser.parse_args()

    # Find log files in the specified folder
    logger.info(f"Searching for log files in: {args.log_folder}")
    log_files = find_log_files(args.log_folder, args.extensions)

    if not log_files:
        logger.error(f"No log files found in {args.log_folder}")
        logger.info(f"Make sure the folder exists and contains files with extensions: {args.extensions}")
        logger.info("You can also specify a different folder with --log-folder /path/to/logs")
        return

    logger.info(f"Found {len(log_files)} potential log files")

    # Validate log files
    valid_log_files = validate_log_files(log_files)

    if not valid_log_files:
        logger.error("No valid log files found after validation")
        return

    logger.info(f"Using {len(valid_log_files)} valid log files for training")

    # Initialize and train the model
    logger.info("Initializing Doc2Vec analyzer...")
    analyzer = Doc2VecLogSimilarity(
        chunk_size=args.chunk_size,
        vector_size=args.vector_size,
        epochs=args.epochs,
        workers=4
    )

    # Prepare training data
    logger.info("Preparing training data...")
    analyzer.prepare_training_data(valid_log_files)

    # Train the model
    logger.info("Training Doc2Vec model...")
    analyzer.train_model()

    # Save the model
    analyzer.save_model(args.model_name)
    logger.info(f"Model saved to {args.model_name}")

    # Demonstrate similarity finding
    logger.info("\n" + "="*50)
    logger.info("SIMILARITY ANALYSIS RESULTS")
    logger.info("="*50)

    # Load the model and find similarities
    finder = LogSimilarityFinder(args.model_name)

    # Get model info
    info = finder.get_model_info()
    logger.info(f"Model contains {info['total_files']} files with {info['total_chunks']} chunks")
    logger.info(f"Vocabulary size: {info['vocabulary_size']} words")

    # Find similar files for each file
    for file_name in info['files']:
        logger.info(f"\nFiles similar to '{file_name}':")
        similar_files = finder.find_similar_files(file_name, top_k=min(3, len(info['files'])-1))

        if similar_files:
            for similar_file, similarity in similar_files:
                logger.info(f"  {similar_file}: {similarity:.4f}")
        else:
            logger.info("  No similar files found")

    logger.info(f"\nTraining completed! Model saved as '{args.model_name}'")
    logger.info(f"You can now use find_similar_logs.py to analyze new files against this model.")
    logger.info(f"Example: python find_similar_logs.py --model {args.model_name} --new-file /path/to/new/log.log")


if __name__ == "__main__":
    main()
