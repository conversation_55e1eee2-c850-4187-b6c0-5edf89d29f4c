#!/usr/bin/env python3
"""
Example usage of the Log Similarity system

This script demonstrates the complete workflow:
1. Create sample log files
2. Train the Doc2Vec model
3. Find similar log files
"""

import os
import tempfile
from pathlib import Path
from log_similarity_doc2vec import Doc2VecLogSimilarity
from find_similar_logs import LogSimilarityFinder
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_sample_log_files(temp_dir: str) -> list:
    """Create sample log files for demonstration."""
    
    # Sample log content for different types of applications
    log_contents = {
        "web_server.log": [
            "2024-01-01 10:00:01 INFO Starting web server on port 8080",
            "2024-01-01 10:00:02 INFO Loading configuration from config.yml",
            "2024-01-01 10:00:03 INFO Database connection established",
            "2024-01-01 10:01:15 INFO GET /api/users - 200 OK - 45ms",
            "2024-01-01 10:01:16 INFO POST /api/login - 200 OK - 123ms",
            "2024-01-01 10:01:17 ERROR Failed to authenticate user: invalid credentials",
            "2024-01-01 10:01:18 WARN Rate limit exceeded for IP *************",
            "2024-01-01 10:02:01 INFO GET /api/products - 200 OK - 67ms",
            "2024-01-01 10:02:02 ERROR Database query timeout after 30 seconds",
            "2024-01-01 10:02:03 INFO Retrying database connection",
        ] * 100,  # Repeat to create larger content
        
        "database.log": [
            "2024-01-01 09:00:01 INFO Database server starting up",
            "2024-01-01 09:00:02 INFO Loading database schema version 2.1.0",
            "2024-01-01 09:00:03 INFO Initializing connection pool with 50 connections",
            "2024-01-01 09:01:15 DEBUG Query executed: SELECT * FROM users WHERE active = true",
            "2024-01-01 09:01:16 DEBUG Query executed: INSERT INTO sessions VALUES (...)",
            "2024-01-01 09:01:17 WARN Slow query detected: execution time 2.5 seconds",
            "2024-01-01 09:01:18 ERROR Deadlock detected in transaction, rolling back",
            "2024-01-01 09:02:01 INFO Checkpoint completed successfully",
            "2024-01-01 09:02:02 ERROR Connection lost to replica server",
            "2024-01-01 09:02:03 INFO Failover to backup server initiated",
        ] * 100,
        
        "application.log": [
            "2024-01-01 11:00:01 INFO Application startup sequence initiated",
            "2024-01-01 11:00:02 INFO Loading modules: auth, user, product, order",
            "2024-01-01 11:00:03 INFO Cache warming up with 1000 entries",
            "2024-01-01 11:01:15 DEBUG Processing user <NAME_EMAIL>",
            "2024-01-01 11:01:16 INFO User registration completed successfully",
            "2024-01-01 11:01:17 ERROR Email service unavailable, queuing notification",
            "2024-01-01 11:01:18 WARN Memory usage at 85%, consider scaling",
            "2024-01-01 11:02:01 INFO Processing batch job: daily_reports",
            "2024-01-01 11:02:02 ERROR Failed to generate report: insufficient permissions",
            "2024-01-01 11:02:03 INFO Batch job completed with 1 error",
        ] * 100,
        
        "web_server_2.log": [
            "2024-01-02 10:00:01 INFO Web server initialization started",
            "2024-01-02 10:00:02 INFO Configuration loaded from /etc/webapp/config.json",
            "2024-01-02 10:00:03 INFO Connected to database successfully",
            "2024-01-02 10:01:15 INFO HTTP GET /api/status - Response: 200 - Duration: 23ms",
            "2024-01-02 10:01:16 INFO HTTP POST /api/authenticate - Response: 200 - Duration: 156ms",
            "2024-01-02 10:01:17 ERROR Authentication failed: user credentials invalid",
            "2024-01-02 10:01:18 WARN Request rate limit hit for client 10.0.0.50",
            "2024-01-02 10:02:01 INFO HTTP GET /api/inventory - Response: 200 - Duration: 89ms",
            "2024-01-02 10:02:02 ERROR Database connection timeout occurred",
            "2024-01-02 10:02:03 INFO Attempting database reconnection",
        ] * 100,
    }
    
    log_files = []
    for filename, content in log_contents.items():
        file_path = os.path.join(temp_dir, filename)
        with open(file_path, 'w') as f:
            f.write('\n'.join(content))
        log_files.append(file_path)
        logger.info(f"Created sample log file: {file_path}")
    
    return log_files


def main():
    """Run the complete example workflow."""
    
    # Create temporary directory for sample files
    with tempfile.TemporaryDirectory() as temp_dir:
        logger.info("Creating sample log files...")
        log_files = create_sample_log_files(temp_dir)
        
        # Initialize and train the model
        logger.info("Initializing Doc2Vec analyzer...")
        analyzer = Doc2VecLogSimilarity(
            chunk_size=10000,
            vector_size=100,
            epochs=10,  # Reduced for demo
            workers=2
        )
        
        # Prepare training data
        logger.info("Preparing training data...")
        analyzer.prepare_training_data(log_files)
        
        # Train the model
        logger.info("Training Doc2Vec model...")
        analyzer.train_model()
        
        # Save the model
        model_path = "demo_log_similarity_model"
        analyzer.save_model(model_path)
        logger.info(f"Model saved to {model_path}")
        
        # Demonstrate similarity finding
        logger.info("\n" + "="*50)
        logger.info("SIMILARITY ANALYSIS RESULTS")
        logger.info("="*50)
        
        # Load the model and find similarities
        finder = LogSimilarityFinder(model_path)
        
        # Get model info
        info = finder.get_model_info()
        logger.info(f"Model contains {info['total_files']} files with {info['total_chunks']} chunks")
        
        # Find similar files for each file
        for file_name in info['files']:
            logger.info(f"\nFiles similar to '{file_name}':")
            similar_files = finder.find_similar_files(file_name, top_k=3)
            
            if similar_files:
                for similar_file, similarity in similar_files:
                    logger.info(f"  {similar_file}: {similarity:.4f}")
            else:
                logger.info("  No similar files found")
        
        # Demonstrate analyzing a new file
        logger.info(f"\n" + "-"*30)
        logger.info("ANALYZING NEW FILE")
        logger.info("-"*30)
        
        # Create a new log file similar to web_server.log
        new_log_content = [
            "2024-01-03 12:00:01 INFO HTTP server starting on port 9090",
            "2024-01-03 12:00:02 INFO Loading server configuration",
            "2024-01-03 12:00:03 INFO Database connection pool initialized",
            "2024-01-03 12:01:15 INFO GET /health - 200 OK - 12ms",
            "2024-01-03 12:01:16 INFO POST /login - 401 Unauthorized - 89ms",
            "2024-01-03 12:01:17 ERROR Login attempt failed: bad password",
            "2024-01-03 12:01:18 WARN Too many requests from 172.16.0.10",
        ] * 50
        
        new_file_path = os.path.join(temp_dir, "new_server.log")
        with open(new_file_path, 'w') as f:
            f.write('\n'.join(new_log_content))
        
        # Analyze the new file
        results = finder.analyze_new_log_file(new_file_path, top_k=3)
        
        if "error" not in results:
            logger.info(f"New file analysis results:")
            logger.info(f"  File: {results['file_path']}")
            logger.info(f"  Chunks created: {results['num_chunks']}")
            logger.info(f"  Average similarity: {results['avg_similarity']:.4f}")
            logger.info(f"  Most similar files:")
            
            for file_name, similarity in results['similar_files']:
                logger.info(f"    {file_name}: {similarity:.4f}")
        else:
            logger.error(f"Error analyzing new file: {results['error']}")
        
        logger.info(f"\nDemo completed! Model saved as '{model_path}'")
        logger.info("You can use this model with your own log files.")


if __name__ == "__main__":
    main()
